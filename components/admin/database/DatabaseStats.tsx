"use client"

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Button } from '@/components/ui/button'
import {
  Database,
  Table,
  BarChart3,
  HardDrive,
  Activity,
  TrendingUp,
  RefreshCw,
  Zap,
  Users,
  FileText,
  Building2,
  Calendar,
  Tag,
  User,
  Key,
  Clock
} from 'lucide-react'

interface DatabaseTable {
  name: string
  displayName: string
  description: string
  category: string
  icon: string
  count: number
  size: number
  columns: Array<{
    name: string
    type: string
    nullable: boolean
    default: string | null
    isPrimary?: boolean
    isForeignKey?: boolean
  }>
  lastModified: string
}

interface DatabaseStatsProps {
  tables: DatabaseTable[]
  onRefresh?: () => void
  isRefreshing?: boolean
}

const iconMap: Record<string, React.ElementType> = {
  Users,
  FileText,
  GraduationCap: Users,
  Building2,
  BookOpen: Calendar,
  Calendar,
  Tag,
  User,
  Key,
  Clock,
  Database
}

export function DatabaseStats({ tables, onRefresh, isRefreshing = false }: DatabaseStatsProps) {
  const [lastRefresh, setLastRefresh] = useState(new Date())

  const totalRecords = tables.reduce((sum, table) => sum + table.count, 0)
  const totalColumns = tables.reduce((sum, table) => sum + table.columns.length, 0)
  const totalSize = tables.reduce((sum, table) => sum + table.size, 0)
  const avgRecordsPerTable = Math.round(totalRecords / tables.length) || 0

  // Group tables by category
  const tablesByCategory = tables.reduce((acc, table) => {
    if (!acc[table.category]) {
      acc[table.category] = []
    }
    acc[table.category].push(table)
    return acc
  }, {} as Record<string, DatabaseTable[]>)

  // Find largest tables
  const largestTables = [...tables]
    .sort((a, b) => b.count - a.count)
    .slice(0, 5)

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleRefresh = async () => {
    if (onRefresh) {
      await onRefresh()
      setLastRefresh(new Date())
    }
  }

  const stats = [
    {
      title: "Total Tables",
      value: tables.length,
      icon: Table,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      description: "Database tables"
    },
    {
      title: "Total Records",
      value: totalRecords.toLocaleString(),
      icon: Database,
      color: "text-green-600",
      bgColor: "bg-green-50",
      description: "All table rows"
    },
    {
      title: "Total Columns",
      value: totalColumns,
      icon: BarChart3,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      description: "All table columns"
    },
    {
      title: "Database Size",
      value: formatBytes(totalSize),
      icon: HardDrive,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      description: "Total storage used"
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header with Refresh */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Database Overview</h2>
          <p className="text-gray-600 mt-1">
            Last updated: {lastRefresh.toLocaleTimeString()}
          </p>
        </div>
        {onRefresh && (
          <Button
            onClick={handleRefresh}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
        )}
      </div>

      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="border-0 bg-white/80 backdrop-blur-sm shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`p-4 rounded-2xl ${stat.bgColor} shadow-lg`}>
                  <stat.icon className={`h-8 w-8 ${stat.color}`} />
                </div>
              </div>
              
              {/* Progress bar effect */}
              <div className="mt-4 h-2 bg-gray-100 rounded-full overflow-hidden">
                <div 
                  className={`h-full ${stat.color.replace('text-', 'bg-')} rounded-full transition-all duration-1000 ease-out`}
                  style={{ 
                    width: `${Math.min(100, (typeof stat.value === 'string' ? parseInt(stat.value.replace(/,/g, '')) : stat.value) / 10)}%`,
                    animationDelay: `${index * 200}ms`
                  }}
                ></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      {/* Category Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Tables by Category */}
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Tag className="w-5 h-5 mr-2 text-indigo-600" />
              Tables by Category
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(tablesByCategory).map(([category, categoryTables]) => {
                const categoryCount = categoryTables.length
                const categoryRecords = categoryTables.reduce((sum, table) => sum + table.count, 0)
                const percentage = (categoryCount / tables.length) * 100

                return (
                  <div key={category} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-600"></div>
                        <span className="font-medium text-gray-900">{category}</span>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">{categoryCount} tables</div>
                        <div className="text-xs text-gray-500">{categoryRecords.toLocaleString()} records</div>
                      </div>
                    </div>
                    <Progress value={percentage} className="h-2" />
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Largest Tables */}
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="w-5 h-5 mr-2 text-green-600" />
              Largest Tables
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {largestTables.map((table, index) => {
                const IconComponent = iconMap[table.icon] || Database
                const maxCount = largestTables[0]?.count || 1
                const percentage = (table.count / maxCount) * 100

                return (
                  <div key={table.name} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center">
                          <IconComponent className="w-4 h-4 text-gray-600" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{table.displayName}</div>
                          <div className="text-xs text-gray-500">{table.category}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">{table.count.toLocaleString()}</div>
                        <div className="text-xs text-gray-500">{table.columns.length} columns</div>
                      </div>
                    </div>
                    <Progress value={percentage} className="h-2" />
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Stats */}
      <Card className="border-0 shadow-lg bg-gradient-to-r from-gray-50 to-slate-50">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="w-5 h-5 mr-2 text-yellow-500" />
            Quick Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{avgRecordsPerTable.toLocaleString()}</div>
              <div className="text-sm text-gray-600">Avg Records/Table</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {Object.keys(tablesByCategory).length}
              </div>
              <div className="text-sm text-gray-600">Categories</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {Math.round(totalColumns / tables.length)}
              </div>
              <div className="text-sm text-gray-600">Avg Columns/Table</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {formatBytes(totalSize / tables.length)}
              </div>
              <div className="text-sm text-gray-600">Avg Size/Table</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 